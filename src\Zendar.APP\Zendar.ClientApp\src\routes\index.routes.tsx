import React, { useEffect } from 'react';
import { configure } from 'react-hotkeys';
import { BrowserRouter, Switch, Route } from 'react-router-dom';
import { GuardProvider, GuardedRoute as Guard } from 'react-router-guards';
import {
  GuardToRoute,
  GuardFunctionRouteProps,
  Next,
  GuardFunction,
} from 'react-router-guards/dist/types';

import PadronizacaoProvider from 'store/Padronizacao/Padronizacao';

import ServiceWorkerWrapper from 'components/ServiceWorkerWrapper';
import { SidebarChat } from 'components/v2/ChatSuporte/SidebarChat';

import Layout from '../components/Layout';
import LoadingPadrao from '../components/Layout/Loading/LoadingPadrao';
import { ModalDesistenciaCadastro } from '../components/Modal/ModalDesistenciaCadastro';
import ConstanteRotas, { ConstanteRotasAlternativas } from '../constants/rotas';
import ConstanteRotasPDV from '../constants/rotasPDV';
import auth from '../modules/auth';
import PaginaNaoExiste from '../pages/PaginaNaoExiste';
import CompartilharExterno from '../pages/PDV/CompartilharExterno';
import SignalR from '../store/SignalR';

import AjudaRoutes from './ajuda.routes';
import AutenticacaoRoutes, { authRoutes } from './autenticacao.routes';
import { AutoAtendimentoRoutes } from './auto-atendimento.routes';
import CamposPersonalizadosRoutes from './campos-personalizados.routes';
import { CardapioRoutes } from './cardapio.routes';
import CategoriaClientesRoutes from './categoria-clientes.routes';
import CategoriaProdutosRoutes from './categoria-produtos.routes';
import ClientesRoutes from './clientes.routes';
import { ConciliacaoDeContasRoutes } from './conciliar-baixas.routes';
import { ConferenciaEstoqueRoutes } from './conferencia-estoque.routes';
import ContaFinanceiraRoutes from './conta-financeira.routes';
import ContaPagarRoutes from './contas-pagar.routes';
import ContaReceberRoutes from './contas-receber.routes';
import ControleCaixaRoutes from './controle-caixa.routes';
import CoresRoutes from './cores.routes';
import { CredenciadoraCartaoRoutes } from './credenciadora-cartao.routes';
import { DashboardGerencialRoutes } from './dashboard-gerencial.routes';
import DashboardRoutes from './dashboard.routes';
import DocumentosFiscaisExportacaoExternoRoutes, {
  documentosFiscaisExportacaoExternoRoutes,
} from './documento-fiscal-exportacao-externo.routes';
import DocumentosFiscaisExportacaoRoutes from './documento-fiscal-exportacao.routes';
import { EntradaMercadoriaRoutes } from './entrada-mercadoria.routes';
import EstadosRoutes from './estados.routes';
import { EtiquetaPersonalizadaRoutes } from './etiquetas-personalizadas.routes';
import { ExtratoRoutes } from './extrato.routes';
import { FaturasRoutes } from './faturas.routes';
import FormaPagamentoRoutes from './forma-pagamento.routes';
import FormaRecebimentoRoutes from './forma-recebimento.routes';
import FornecedoresRoutes from './fornecedores.routes';
import { FrenteCaixaRoutes } from './frente-caixa.routes';
import HistoricoAcoesRoutes from './historico-acoes.routes';
import { HistoricoProdutoRoutes } from './historico-produto.routes';
import { HistoricoVendasMetasComissoesRoutes } from './historico-vendas-metas-comissoes.routes';
import ImportarClientesRoutes from './importar-clientes.routes';
import ImportarContasReceberRoutes from './importar-contas-receber.routes';
import ImportarFornecedoresRoutes from './importar-fornecedores.routes';
import ImportarProdutosRoutes from './importar-produtos.routes';
import { LocalEstoqueRoutes } from './local-estoque.routes';
import { LojaAplicativoRoutes } from './loja-aplicativo.routes';
import LojasRoutes from './lojas.routes';
import { ManifestacaoDestinatarioRoutes } from './manifestacao-destinatario.routes';
import MarcasRoutes from './marcas.routes';
import { MetasComissoesRoutes } from './metas-comissoes.routes';
import { MovimentacaoEstoqueRoutes } from './movimentacao-estoque.routes';
import MultasEJuros from './MultasEJuros.routes';
import NotasFiscaisRoutes from './notas-fiscais.routes';
import PadronizacaoRoutes from './padronizacao.routes';
import { PdvAutonomoRoutes } from './pdv-autonomo.routes';
import PDVRoutes from './pdv.routes';
import PerfisUsuariosRoutes from './perfil-usuarios.routes';
import PlanoContasRoutes from './plano-contas.routes';
import ProdutosRoutes from './produtos.routes';
import { PromocaoCadastrarRoutes } from './promocao-cadastrar.routes';
import { PromocaoRoutes } from './promocao.routes';
import { RecebimentoContasRoutes } from './recebimento-contas.routes';
import RegrasFiscaisRoutes from './regras-fiscais.routes';
import { RelatorioCatalogoProdutosRoutes } from './relatorio-catalogo-produto.routes';
import { RelatorioComprasRoutes } from './relatorio-compras.routes';
import { RelatorioEstoqueRoutes } from './relatorio-estoque.routes';
import { RelatorioInformacoesClientes } from './relatorio-informacoes-clientes.routes';
import { RelatorioInventarioRoutes } from './relatorio-inventario.routes';
import { RelatorioOperacoesRoutes } from './relatorio-operacoes.routes';
import { RelatorioVendasMetasComissoesRoutes } from './relatorio-vendas-metas-comissoes.routes';
import { RelatorioVendasRoutes } from './relatorio-vendas.routes';
import { RelatorioDeCurvaABC } from './relatorioCurvaABC.routes';
import RelatorioClientesPersonalizados from './relatorioPersonalizadoClientes.routes';
import { RelatorioProdutosPersonalizadosRoutes } from './relatorios-produtos-personalizados.routes';
import { SmartPosRoutes } from './smart-pos.routes';
import { TabelaPrecosRoutes } from './tabela-precos.routes';
import TamanhosRoutes from './tamanhos.routes';
import { TransferenciaEstoqueRoutes } from './transferencia-estoque.routes';
import TransportadorasRoutes from './transportadoras.routes';
import { TrayEtapasRoutes } from './tray-etapas.routes';
import { PainelAdmTrayRoutes } from './tray.routes';
import UnidadesMedidaRoutes from './unidades-medida.routes';
import UsuariosRoutes from './usuarios.routes';
import { CardapioVendasRoutes } from './vendas-cardapio.routes';
import { SmartPosVendasRoutes } from './vendas-smart-pos.routes';
import Operacoes from './vendas.routes';
import VendedoresRoutes from './vendedores.routes';
// Importações
import VouchersRoutes from './vouchers.routes';
import { ZoopConfigRoutes } from './zoop-config.routes';
import { ZoopGerencialRoutes } from './zoop-geral.routes';
import { ZoopRoutes } from './zoop.routes';

const autenticadoComRotaParaPaginaAutenticacao = (path: string) =>
  auth.autenticado() &&
  (path === ConstanteRotas.LOGIN ||
    path === ConstanteRotas.HOME ||
    path === ConstanteRotas.RECUPERAR_SENHA ||
    path === `${ConstanteRotas.REDEFINIR_SENHA}/:token` ||
    path === ConstanteRotas.SENHA_ENVIADA_COM_SUCESSO);

const requireLogin: GuardFunction = (
  to: GuardToRoute,
  from: GuardFunctionRouteProps | null,
  next: Next
) => {
  const dominio = window.location.host.split('/')[0].toLowerCase();

  if (dominio !== 'app') {
    if (to.meta.auth) {
      if (auth.autenticado()) {
        next();
      }
      next.redirect(ConstanteRotas.LOGIN);
      return;
    }

    if (autenticadoComRotaParaPaginaAutenticacao(to.location.pathname)) {
      next.redirect(ConstanteRotas.DASHBOARD);
      return;
    }
  }

  if (
    to.location.pathname === ConstanteRotas.LOGIN ||
    to.location.pathname === ConstanteRotas.HOME
  ) {
    auth.dominioExistente(to.location.pathname, next);
  } else {
    next();
  }
};

const Routes: React.FC = () => {
  useEffect(() => {
    configure({ ignoreTags: [] });
  }, []);

  return (
    <BrowserRouter
      getUserConfirmation={(message, callback) =>
        ModalDesistenciaCadastro({ textoMensagem: message, callback })
      }
    >
            <GuardProvider
        guards={[requireLogin]}
        loading={() => (
          <div
            style={{
              position: 'fixed',
              height: '100%',
              width: '100%',
              maxWidth: '9999px',
              top: 0,
              left: 0,
            }}
          >
            <LoadingPadrao />
          </div>
        )}
      >
      >
        <ServiceWorkerWrapper />
        <Switch>
          <Route
            path={[
              ...authRoutes.map((authRoute) => authRoute.path),
              ...documentosFiscaisExportacaoExternoRoutes.map(
                (documentosFiscaisExportacaoExternoRoute) =>
                  documentosFiscaisExportacaoExternoRoute.path
              ),
              ConstanteRotas.COMPARTILHAR_EXTERNO,
              ...Object.values(ConstanteRotasPDV),
              ...Object.values(ConstanteRotas),
              ...Object.values(ConstanteRotasAlternativas),
              '',
            ]}
            exact
          >
            <SignalR>
              <Route
                key="autenticacao-routes"
                path={authRoutes.map((route) => route.path)}
                exact
              >
                <AutenticacaoRoutes />
              </Route>
              <Route
                key="documento-fiscal-exportacao-externo-routes"
                path={documentosFiscaisExportacaoExternoRoutes.map(
                  (route) => route.path
                )}
                exact
              >
                <DocumentosFiscaisExportacaoExternoRoutes />
              </Route>
              <Route
                key="compartilhar-notas-fiscais-externo"
                path={ConstanteRotas.COMPARTILHAR_EXTERNO}
                exact
                render={(props) => <CompartilharExterno {...props} />}
              />
              <Route
                path={[
                  ...Object.values(ConstanteRotasPDV),
                  ...Object.values(ConstanteRotas),
                  ...Object.values(ConstanteRotasAlternativas),
                  '',
                ].filter(
                  (route) =>
                    ![
                      ...authRoutes.map((authRoute) => authRoute.path),
                      ...documentosFiscaisExportacaoExternoRoutes.map(
                        (documentosFiscaisExportacaoExternoRoute) =>
                          documentosFiscaisExportacaoExternoRoute.path
                      ),
                      ConstanteRotas.COMPARTILHAR_EXTERNO,
                      '',
                    ].includes(route)
                )}
                exact
              >
                <SidebarChat />

                <PadronizacaoProvider>
                  <Route path={Object.values(ConstanteRotasPDV)}>
                    <PDVRoutes />
                  </Route>
                  <Route
                    exact
                    path={[
                      ...Object.values(ConstanteRotasAlternativas).filter(
                        (item) => item !== '/'
                      ),
                    ]}
                  >
                    <>
                      {HistoricoVendasMetasComissoesRoutes}
                      {RelatorioVendasMetasComissoesRoutes}
                      {SmartPosRoutes}
                      {CardapioRoutes}
                      {TrayEtapasRoutes}
                      {ZoopRoutes}
                    </>
                  </Route>
                  <Route
                    exact
                    path={[
                      ...Object.values(ConstanteRotas).filter(
                        (item) => item !== '/'
                      ),
                    ]}
                  >
                    <Layout>
                      {DashboardRoutes}

                      {AjudaRoutes}
                      {VendedoresRoutes}

                      <Operacoes />

                      {CategoriaClientesRoutes}

                      <ProdutosRoutes />

                      {ImportarProdutosRoutes}

                      {ImportarContasReceberRoutes}

                      {TransportadorasRoutes}

                      {EstadosRoutes}

                      {MarcasRoutes}

                      {CoresRoutes}

                      {TamanhosRoutes}

                      {PromocaoRoutes}

                      <PromocaoCadastrarRoutes />

                      {LocalEstoqueRoutes}

                      <MovimentacaoEstoqueRoutes />

                      {TransferenciaEstoqueRoutes}

                      {TabelaPrecosRoutes}

                      {HistoricoProdutoRoutes}

                      <ConferenciaEstoqueRoutes />

                      {ContaFinanceiraRoutes}

                      {PlanoContasRoutes}

                      {FormaPagamentoRoutes}

                      {FormaRecebimentoRoutes}

                      {RegrasFiscaisRoutes}

                      {UnidadesMedidaRoutes}

                      {HistoricoAcoesRoutes}

                      {ZoopConfigRoutes}

                      {CategoriaProdutosRoutes}

                      {ClientesRoutes}

                      {ImportarClientesRoutes}

                      {FornecedoresRoutes}

                      {EtiquetaPersonalizadaRoutes}

                      {ImportarFornecedoresRoutes}

                      {NotasFiscaisRoutes}

                      {RelatorioInventarioRoutes}

                      {PerfisUsuariosRoutes}

                      {UsuariosRoutes}

                      {LojasRoutes}

                      {PadronizacaoRoutes}

                      {CamposPersonalizadosRoutes}

                      {DocumentosFiscaisExportacaoRoutes}

                      {EntradaMercadoriaRoutes}

                      {ExtratoRoutes}

                      {RecebimentoContasRoutes}

                      {FaturasRoutes}

                      {RelatorioInformacoesClientes}

                      {ContaPagarRoutes}

                      {ContaReceberRoutes}

                      {FrenteCaixaRoutes}

                      {AutoAtendimentoRoutes}

                      {ControleCaixaRoutes}

                      {ConciliacaoDeContasRoutes}

                      {ManifestacaoDestinatarioRoutes}

                      {RelatorioProdutosPersonalizadosRoutes}

                      {RelatorioClientesPersonalizados}

                      {RelatorioOperacoesRoutes}

                      {MultasEJuros}

                      {RelatorioDeCurvaABC}

                      {MetasComissoesRoutes}

                      {LojaAplicativoRoutes}

                      {VouchersRoutes}
                      {DashboardGerencialRoutes}

                      {SmartPosVendasRoutes}

                      {CardapioVendasRoutes}

                      {ZoopGerencialRoutes}

                      {PdvAutonomoRoutes}
                      {RelatorioVendasRoutes}

                      {RelatorioComprasRoutes}

                      {RelatorioEstoqueRoutes}

                      {RelatorioCatalogoProdutosRoutes}

                      {PainelAdmTrayRoutes}

                      {CredenciadoraCartaoRoutes}
                    </Layout>
                  </Route>
                </PadronizacaoProvider>
              </Route>
            </SignalR>
          </Route>

          <Guard key="404" path="*" component={PaginaNaoExiste} />
        </Switch>
      </GuardProvider>
    </BrowserRouter>
  );
};
export default Routes;
