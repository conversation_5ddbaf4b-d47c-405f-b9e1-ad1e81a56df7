declare module 'react-router-guards' {
  import { ReactNode, ComponentType } from 'react';
  import { RouteProps } from 'react-router-dom';

  export interface BaseGuardProps extends RouteProps {
    meta?: any;
  }

  export interface GuardProviderProps {
    guards: GuardFunction[];
    loading?: ComponentType | (() => ReactNode);
    error?: ComponentType;
    children: ReactNode;
  }

  export interface GuardedRouteProps extends BaseGuardProps {
    component?: ComponentType<any>;
    render?: (props: any) => ReactNode;
  }

  export type GuardToRoute = {
    location: {
      pathname: string;
      search: string;
      hash: string;
      state?: any;
    };
    meta: any;
  };

  export type GuardFunctionRouteProps = {
    location: {
      pathname: string;
      search: string;
      hash: string;
      state?: any;
    };
    meta: any;
  };

  export type Next = {
    (): void;
    redirect: (path: string) => void;
  };

  export type GuardFunction = (
    to: GuardToRoute,
    from: GuardFunctionRouteProps | null,
    next: Next
  ) => void;

  export type PropsWithMeta<T> = T & {
    meta?: any;
  };

  export const GuardProvider: ComponentType<GuardProviderProps>;
  export const GuardedRoute: ComponentType<GuardedRouteProps>;
}
